import { NextRequest, NextResponse } from 'next/server';
import { getIntegration, hasIntegration, createAgent, getIntegrationList } from '@workspace/agent-registry/server';
import WebSocket from 'ws';
import { generateMessageId } from '@workspace/shared';

/**
 * Mario WebSocket API 端点配置
 * 实际的Mario服务运行在WebSocket上
 * 修改为本地Python LangGraph服务
 */
const MARIO_WS_ENDPOINT = process.env.MARIO_WS_ENDPOINT || 'ws://0.0.0.0:8080/ws/chat';

/**
 * WebSocket连接管理器
 * 用于管理与Mario服务的WebSocket连接
 */
class MarioWebSocketManager {
  private static connections = new Map<string, WebSocket>();

  /**
   * 获取或创建WebSocket连接
   * @param agentId - Agent ID
   * @param sessionId - 会话ID
   */
  static async getConnection(agentId: string, sessionId?: string): Promise<WebSocket> {
    const connectionKey = `${agentId}-${sessionId || 'default'}`;

    if (this.connections.has(connectionKey)) {
      const existingWs = this.connections.get(connectionKey)!;
      if (existingWs.readyState === WebSocket.OPEN) {
        return existingWs;
      } else {
        this.connections.delete(connectionKey);
      }
    }

    return new Promise((resolve, reject) => {
      try {
        console.log(`🔌 Connecting to Mario WebSocket: ${MARIO_WS_ENDPOINT}`);
        console.log(`📋 Connection params: agentId=${agentId}, sessionId=${sessionId}`);

        const ws = new WebSocket(MARIO_WS_ENDPOINT);

        ws.on('open', () => {
          console.log('✅ WebSocket connection established');
          this.connections.set(connectionKey, ws);
          resolve(ws);
        });

        ws.on('error', (error) => {
          console.error('❌ WebSocket connection error:', error);
          reject(error);
        });

        ws.on('close', () => {
          console.log('🔌 WebSocket connection closed');
          this.connections.delete(connectionKey);
        });
      } catch (error) {
        console.error('❌ Failed to create WebSocket connection:', error);
        reject(error);
      }
    });
  }

  /**
   * 发送消息到Mario WebSocket
   * @param ws - WebSocket连接
   * @param message - 要发送的消息
   */
  static async sendMessage(ws: WebSocket, message: any): Promise<string> {
    return new Promise((resolve, reject) => {
      if (ws.readyState !== WebSocket.OPEN) {
        reject(new Error('WebSocket connection is not open'));
        return;
      }

      // 监听响应消息
      const messageHandler = (data: WebSocket.Data) => {
        try {
          const response = JSON.parse(data.toString());
          ws.off('message', messageHandler);
          resolve(response.content || response.message || 'No response content');
        } catch (error) {
          ws.off('message', messageHandler);
          reject(new Error('Failed to parse WebSocket response'));
        }
      };

      ws.on('message', messageHandler);

      // 发送消息
      ws.send(JSON.stringify(message), (error) => {
        if (error) {
          ws.off('message', messageHandler);
          reject(error);
        }
      });

      // 设置超时
      setTimeout(() => {
        ws.off('message', messageHandler);
        reject(new Error('WebSocket response timeout'));
      }, 30000); // 30秒超时
    });
  }

  /**
   * 关闭WebSocket连接
   * @param agentId - Agent ID
   * @param sessionId - 会话ID
   */
  static closeConnection(agentId: string, sessionId?: string): void {
    const connectionKey = `${agentId}-${sessionId || 'default'}`;
    const ws = this.connections.get(connectionKey);
    if (ws) {
      ws.close();
      this.connections.delete(connectionKey);
    }
  }
}

/**
 * Mario WebSocket 代理处理器
 * 将HTTP请求转发到Mario WebSocket服务
 */
export const POST = async (request: NextRequest) => {
  try {
    console.log('=== Mario WebSocket Proxy Called ===');
    console.log('Request URL:', request.url);
    console.log('Request method:', request.method);
    console.log('Mario WebSocket Endpoint:', MARIO_WS_ENDPOINT);

    // 从 URL 路径中提取 agentId
    const urlParts = request.url.split('/');
    const lastPart = urlParts.pop() || '';
    const agentId = lastPart.split('?')[0];
    console.log('🔍 Extracted agentId:', agentId);

    // 从查询参数中提取 sessionId
    const url = new URL(request.url);
    const sessionId = url.searchParams.get('sessionId');
    console.log('🔍 Extracted sessionId:', sessionId);

    if (!agentId) {
      console.error('❌ Agent ID not provided in URL');
      return NextResponse.json(
        {
          error: 'Agent ID not provided',
          details: 'Agent ID is required in the URL path'
        },
        { status: 400 }
      );
    }

    console.log('✅ Using agent ID:', agentId);
    console.log('✅ Using session ID:', sessionId || 'default');

    // 验证 agent 配置是否存在
    console.log('🔍 Checking if agent configuration exists...');
    const agentInfo = await getIntegration(agentId);
    if (!agentInfo) {
      console.error(`❌ Agent configuration for ${agentId} not found`);
      const availableAgents = await getIntegrationList();
      console.log(
        'Available agents:',
        availableAgents.map((a) => a.id)
      );
      return NextResponse.json(
        {
          error: 'Agent configuration not found',
          details: `Agent ${agentId} is not configured`,
          requestedAgent: agentId,
          availableAgents: availableAgents.map((a) => a.id)
        },
        { status: 404 }
      );
    }

    console.log('✅ Agent configuration found:', agentInfo.name);

    // 检查 agent 是否存在于注册中心
    console.log('🔍 Checking if agent exists in registry...');
    if (!(await hasIntegration(agentId))) {
      console.error(`❌ Agent ${agentId} not found in registry`);
      return NextResponse.json(
        {
          error: 'Agent not found in registry',
          details: `Agent ${agentId} is not registered in the agent registry`,
          requestedAgent: agentId,
          availableAgents: (await getIntegrationList()).map((integration) => integration.id)
        },
        { status: 404 }
      );
    }

    console.log('✅ Agent exists in registry');

    // 获取请求体
    const body = await request.json();
    console.log('📨 Request body:', JSON.stringify(body, null, 2));

    // 从注册中心获取指定的 Mastra agent 实例（用于获取配置信息）
    console.log('🚀 Getting Mastra agent configuration...');
    const mastraAgent = await createAgent(agentId);
    console.log('✅ Retrieved Mastra agent configuration');
    console.log('📝 Agent info:', {
      name: mastraAgent.name,
      hasInstructions: !!mastraAgent.instructions,
      instructionsLength: mastraAgent.instructions?.length || 0
    });

    // 准备发送到Mario WebSocket的消息
    const marioMessage = {
      agentId,
      sessionId: sessionId || `session-${generateMessageId()}`,
      message: body.message || body.content || '',
      messages: body.messages || [],
      timestamp: new Date().toISOString(),
      agentConfig: {
        name: mastraAgent.name,
        instructions: mastraAgent.instructions
      }
    };

    console.log('📤 Prepared Mario message:', {
      agentId: marioMessage.agentId,
      sessionId: marioMessage.sessionId,
      messageLength: marioMessage.message.length,
      historyLength: marioMessage.messages.length
    });

    // 建立真正的WebSocket连接到Mario服务
    console.log('🔌 Establishing WebSocket connection to Mario service');
    console.log(`📡 Connecting to: ${MARIO_WS_ENDPOINT}`);

    try {
      // 获取WebSocket连接
      const ws = await MarioWebSocketManager.getConnection(agentId, marioMessage.sessionId);
      console.log('✅ WebSocket connection established');

      // 发送消息到Mario WebSocket服务
      console.log('📤 Sending message to Mario WebSocket service');
      const responseContent = await MarioWebSocketManager.sendMessage(ws, marioMessage);
      console.log('✅ Received response from Mario WebSocket service');

      // 构建响应对象
      const response = {
        id: `mario-response-${generateMessageId()}`,
        role: 'assistant' as const,
        content: responseContent,
        timestamp: new Date().toISOString(),
        agentId,
        sessionId: marioMessage.sessionId
      };

      // 返回与前端期望格式匹配的响应
      return NextResponse.json({
        success: true,
        mode: 'mario-websocket',
        agentId,
        sessionId: marioMessage.sessionId,
        response,
        websocketEndpoint: MARIO_WS_ENDPOINT
      });
    } catch (wsError) {
      console.error('❌ WebSocket communication failed:', wsError);

      // WebSocket连接失败时的降级处理
      const fallbackResponse = {
        id: `mario-fallback-${generateMessageId()}`,
        role: 'assistant' as const,
        content: `抱歉，Mario WebSocket服务暂时不可用。错误信息：${wsError instanceof Error ? wsError.message : '未知错误'}`,
        timestamp: new Date().toISOString(),
        agentId,
        sessionId: marioMessage.sessionId
      };

      return NextResponse.json(
        {
          success: false,
          mode: 'mario-websocket-fallback',
          agentId,
          sessionId: marioMessage.sessionId,
          response: fallbackResponse,
          error: {
            type: 'websocket_connection_failed',
            message: wsError instanceof Error ? wsError.message : '未知错误',
            websocketEndpoint: MARIO_WS_ENDPOINT
          }
        },
        { status: 503 }
      );
    }
  } catch (error) {
    console.error('💥 Mario WebSocket Proxy error:', error);
    console.error('🔍 Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : 'No stack trace',
      name: error instanceof Error ? error.name : 'Unknown error type'
    });

    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json(
      {
        error: 'Mario WebSocket Proxy error',
        details: errorMessage,
        stack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString(),
        agentId: request.url.split('/').pop(),
        mode: 'mario-websocket'
      },
      { status: 500 }
    );
  }
};

/**
 * 处理 GET 请求 - 用于健康检查和状态查询
 */
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const agentId = pathParts[pathParts.length - 1];
    const sessionId = url.searchParams.get('sessionId');

    console.log('🔍 Mario API Health Check - Agent:', agentId, 'Session:', sessionId);

    // 验证 agent 是否存在
    if (agentId && !(await hasIntegration(agentId))) {
      return NextResponse.json(
        {
          error: 'Agent not found',
          agentId
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      status: 'healthy',
      mode: 'mario',
      agentId,
      sessionId,
      timestamp: new Date().toISOString(),
      availableAgents: (await getIntegrationList()).map((a) => ({ id: a.id, name: a.name }))
    });
  } catch (error) {
    console.error('💥 Mario API health check error:', error);
    return NextResponse.json(
      {
        status: 'error',
        mode: 'mario',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
